@echo off
echo Testing MACIT environment...
call conda activate MACIT
if errorlevel 1 (
    echo Failed to activate MACIT environment
    exit /b 1
)

echo Environment activated successfully
python -c "import sys; print('Python version:', sys.version)"
python -c "import camel; print('camel-ai package: OK')"
python -c "import openai; print('openai package: OK')"
python -c "import json; print('json package: OK')"

echo All packages are available
pause
